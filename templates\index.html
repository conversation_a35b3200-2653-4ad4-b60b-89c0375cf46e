<!DOCTYPE html>
<html>
<head>
    <title>文件上传系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #007AFF;
            --secondary-color: #34C759;
            --danger-color: #FF3B30;
            --background-color: #F2F2F7;
            --card-color: #FFFFFF;
            --text-color: #1C1C1E;
            --border-radius: 16px;
        }
        
        * {
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }
        
        body {
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .card {
            background-color: var(--card-color);
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        h2 {
            font-size: 20px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 16px;
        }
        
        .qr-code {
            max-width: 150px;
            height: auto;
            border-radius: 8px;
        }
        
        .qr-section {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }
        
        .text-input-area {
            flex-grow: 1;
        }
        
        .text-input, .text-area, .url-input {
            width: 100%;
            padding: 16px;
            font-size: 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            background-color: rgba(0, 0, 0, 0.02);
            resize: vertical;
            transition: all 0.2s;
        }
        
        .text-input:focus, .text-area:focus, .url-input:focus, .file-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
        }
        
        .text-area {
            height: 150px;
            margin-bottom: 16px;
        }
        
        .button {
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 500;
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .primary-btn {
            background-color: var(--primary-color);
        }
        
        .primary-btn:hover {
            background-color: #0062CC;
            transform: translateY(-1px);
        }
        
        .success-btn {
            background-color: var(--secondary-color);
        }
        
        .success-btn:hover {
            background-color: #2BB14C;
            transform: translateY(-1px);
        }
        
        .danger-btn {
            background-color: var(--danger-color);
        }
        
        .danger-btn:hover {
            background-color: #E0352B;
            transform: translateY(-1px);
        }
        
        .purple-btn {
            background-color: #AF52DE;
        }
        
        .purple-btn:hover {
            background-color: #9541C8;
            transform: translateY(-1px);
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .file-input-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .file-input {
            flex-grow: 1;
            padding: 14px;
            font-size: 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .result-message {
            margin-top: 12px;
            padding: 10px;
            border-radius: 8px;
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--secondary-color);
        }
        
        .file-list {
            margin-top: 16px;
        }
        
        .file-item {
            padding: 12px 16px;
            margin-bottom: 8px;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 10px;
            transition: all 0.2s;
        }
        
        .file-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .file-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .url-qr-section {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        #qrResult {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 16px;
        }
        
        .folder-path {
            font-size: 14px;
            color: #8E8E93;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h2>扫描二维码访问</h2>
            <div class="qr-section">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data={{ access_url }}" alt="QR Code" class="qr-code">
                <div class="text-input-area">
                    <textarea class="text-input" id="textInput" placeholder="在此输入文本内容..."></textarea>
                    <button onclick="saveText()" class="button success-btn" style="margin-top: 12px;">保存为文本文件</button>
                    <div id="textSaveResult" class="result-message" style="display: none;"></div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="section-header">
                <h2>URL二维码生成</h2>
            </div>
            <div class="url-qr-section">
                <input type="text" id="urlInput" class="url-input" placeholder="输入URL生成二维码...">
                <button onclick="generateQR()" class="button primary-btn">生成</button>
                <button onclick="clearURL()" class="button danger-btn">清空</button>
            </div>
            <div id="qrResult"></div>
        </div>

        <div class="card">
            <div class="section-header">
                <h2>文本传输</h2>
                <button onclick="clearText()" class="button danger-btn">清空并提交</button>
            </div>
            <textarea class="text-area" id="textArea" placeholder="在此输入文本..."></textarea>
            <div class="button-group">
                <button onclick="refreshText()" class="button primary-btn">刷新</button>
                <button onclick="submitText()" class="button success-btn">提交</button>
                <button onclick="copyText()" class="button purple-btn">复制</button>
            </div>
        </div>

        <div class="card">
            <h2>上传文件</h2>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="file-input-container">
                    <input type="file" name="files[]" multiple class="file-input">
                    <button type="button" onclick="uploadFiles()" class="button success-btn">上传</button>
                </div>
            </form>
            <div id="uploadResult" class="result-message" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="section-header">
                <h2>已上传文件列表</h2>
                <span class="folder-path">本地目录：{{ upload_folder }}</span>
            </div>
            <div class="file-list">
                {% for file in files %}
                <div class="file-item">
                    <a href="{{ url_for('download_file', filename=file) }}" class="file-link">{{ file }}</a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        function uploadFiles() {
            var form = document.getElementById('uploadForm');
            var formData = new FormData(form);
            var resultElement = document.getElementById('uploadResult');
            
            resultElement.style.display = 'none';
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                resultElement.innerHTML = result;
                resultElement.style.display = 'block';
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                resultElement.innerHTML = '上传失败！';
                resultElement.style.display = 'block';
            });
        }

        function saveText() {
            var text = document.getElementById('textInput').value;
            var resultElement = document.getElementById('textSaveResult');
            
            resultElement.style.display = 'none';
            
            if (!text) {
                resultElement.innerHTML = '请输入文本内容';
                resultElement.style.display = 'block';
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/save-text', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                resultElement.innerHTML = result;
                resultElement.style.display = 'block';
                document.getElementById('textInput').value = '';
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                resultElement.innerHTML = '保存失败！';
                resultElement.style.display = 'block';
            });
        }

        function submitText() {
            const text = document.getElementById('textArea').value;
            if (!text) {
                return;
            }
            
            var formData = new FormData();
            formData.append('text', text);
            
            fetch('/update-text', {
                method: 'POST',
                body: formData
            });
        }

        function refreshText() {
            fetch('/get-text')
            .then(response => response.text())
            .then(text => {
                document.getElementById('textArea').value = text;
            });
        }

        function copyText() {
            const textArea = document.getElementById('textArea');
            textArea.select();
            document.execCommand('copy');
            // 取消选中
            window.getSelection().removeAllRanges();
            
            // 显示复制成功的视觉反馈
            const originalText = textArea.style.borderColor;
            textArea.style.borderColor = 'var(--secondary-color)';
            setTimeout(() => {
                textArea.style.borderColor = originalText;
            }, 500);
        }

        function clearText() {
            document.getElementById('textArea').value = '';
            // 同时清空服务器存储的文本
            fetch('/update-text', {
                method: 'POST',
                body: new FormData()
            });
        }

        function generateQR() {
            const urlInput = document.getElementById('urlInput');
            const qrResult = document.getElementById('qrResult');
            const url = urlInput.value.trim();
            
            if (!url) {
                alert('请输入URL！');
                return;
            }
            
            // 使用QR Server API生成二维码
            const qrImage = document.createElement('img');
            qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(url)}`;
            qrImage.alt = 'QR Code';
            qrImage.className = 'qr-code';
            
            // 清除之前的二维码（如果有）
            qrResult.innerHTML = '';
            qrResult.appendChild(qrImage);
        }

        function clearURL() {
            document.getElementById('urlInput').value = '';
            document.getElementById('qrResult').innerHTML = '';
        }
        
        // 页面加载时自动刷新文本
        window.onload = function() {
            refreshText();
        };
    </script>
</body>
</html>
