<!DOCTYPE html>
<html>
<head>
    <title>文件上传系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #007AFF;
            --secondary-color: #34C759;
            --danger-color: #FF3B30;
            --background-color: #FFFFFF;
            --text-color: #1C1C1E;
            --border-color: #E5E5EA;
            --section-bg: #F8F9FA;
        }

        * {
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            line-height: 1.5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
        }

        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        h1 {
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 30px 0;
            text-align: center;
            color: var(--primary-color);
        }

        h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 20px 0;
            color: var(--text-color);
        }

        .qr-code {
            max-width: 150px;
            height: auto;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .qr-section {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 20px;
        }

        .text-input-area {
            flex-grow: 1;
        }

        .text-input, .text-area, .url-input, .file-input {
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--background-color);
            resize: vertical;
            transition: all 0.2s;
        }

        .text-input:focus, .text-area:focus, .url-input:focus, .file-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .text-area {
            height: 120px;
            margin-bottom: 16px;
        }

        .button {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-block;
        }

        .primary-btn {
            background-color: var(--primary-color);
        }

        .primary-btn:hover {
            background-color: #0062CC;
        }

        .success-btn {
            background-color: var(--secondary-color);
        }

        .success-btn:hover {
            background-color: #2BB14C;
        }

        .danger-btn {
            background-color: var(--danger-color);
        }

        .danger-btn:hover {
            background-color: #E0352B;
        }

        .purple-btn {
            background-color: #AF52DE;
        }

        .purple-btn:hover {
            background-color: #9541C8;
        }

        .button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .file-input-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .file-input {
            flex-grow: 1;
        }

        .result-message {
            margin-top: 12px;
            padding: 12px;
            border-radius: 6px;
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--secondary-color);
            border: 1px solid rgba(52, 199, 89, 0.2);
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.2s;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-item:hover {
            background-color: var(--section-bg);
            margin: 0 -10px;
            padding: 10px;
            border-radius: 4px;
        }

        .file-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 400;
        }

        .file-link:hover {
            text-decoration: underline;
        }

        .url-qr-section {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            align-items: flex-start;
        }

        #qrResult {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 16px;
        }

        .folder-path {
            font-size: 12px;
            color: #8E8E93;
            margin-top: 5px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件上传系统</h1>

        <div class="section">
            <h2>扫描二维码访问</h2>
            <div class="qr-section">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data={{ access_url }}" alt="QR Code" class="qr-code">
                <div class="text-input-area">
                    <div class="input-group">
                        <label class="input-label">快速保存文本</label>
                        <textarea class="text-input" id="textInput" placeholder="在此输入文本内容..."></textarea>
                        <button onclick="saveText()" class="button success-btn" style="margin-top: 12px;">保存为文本文件</button>
                        <div id="textSaveResult" class="result-message" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>URL二维码生成</h2>
            <div class="url-qr-section">
                <input type="text" id="urlInput" class="url-input" placeholder="输入URL生成二维码...">
                <button onclick="generateQR()" class="button primary-btn">生成</button>
                <button onclick="clearURL()" class="button danger-btn">清空</button>
            </div>
            <div id="qrResult"></div>
        </div>

        <div class="section">
            <div class="section-header">
                <h2>文本传输</h2>
                <button onclick="clearText()" class="button danger-btn">清空并提交</button>
            </div>
            <div class="input-group">
                <textarea class="text-area" id="textArea" placeholder="在此输入文本..."></textarea>
                <div class="button-group">
                    <button onclick="refreshText()" class="button primary-btn">刷新</button>
                    <button onclick="submitText()" class="button success-btn">提交</button>
                    <button onclick="copyText()" class="button purple-btn">复制</button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>上传文件</h2>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="file-input-container">
                    <input type="file" name="files[]" multiple class="file-input">
                    <button type="button" onclick="uploadFiles()" class="button success-btn">上传</button>
                </div>
            </form>
            <div id="uploadResult" class="result-message" style="display: none;"></div>
        </div>

        <div class="section">
            <div class="section-header">
                <h2>已上传文件列表</h2>
            </div>
            <div class="folder-path">本地目录：{{ upload_folder }}</div>
            <div class="file-list">
                {% for file in files %}
                <div class="file-item">
                    <a href="{{ url_for('download_file', filename=file) }}" class="file-link">{{ file }}</a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        function uploadFiles() {
            var form = document.getElementById('uploadForm');
            var formData = new FormData(form);
            var resultElement = document.getElementById('uploadResult');

            resultElement.style.display = 'none';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                resultElement.innerHTML = result;
                resultElement.style.display = 'block';
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                resultElement.innerHTML = '上传失败！';
                resultElement.style.display = 'block';
            });
        }

        function saveText() {
            var text = document.getElementById('textInput').value;
            var resultElement = document.getElementById('textSaveResult');

            resultElement.style.display = 'none';

            if (!text) {
                resultElement.innerHTML = '请输入文本内容';
                resultElement.style.display = 'block';
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/save-text', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(result => {
                resultElement.innerHTML = result;
                resultElement.style.display = 'block';
                document.getElementById('textInput').value = '';
                // 刷新页面以更新文件列表
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                resultElement.innerHTML = '保存失败！';
                resultElement.style.display = 'block';
            });
        }

        function submitText() {
            const text = document.getElementById('textArea').value;
            if (!text) {
                return;
            }

            var formData = new FormData();
            formData.append('text', text);

            fetch('/update-text', {
                method: 'POST',
                body: formData
            });
        }

        function refreshText() {
            fetch('/get-text')
            .then(response => response.text())
            .then(text => {
                document.getElementById('textArea').value = text;
            });
        }

        function copyText() {
            const textArea = document.getElementById('textArea');
            textArea.select();
            document.execCommand('copy');
            // 取消选中
            window.getSelection().removeAllRanges();

            // 显示复制成功的视觉反馈
            const originalText = textArea.style.borderColor;
            textArea.style.borderColor = 'var(--secondary-color)';
            setTimeout(() => {
                textArea.style.borderColor = originalText;
            }, 500);
        }

        function clearText() {
            document.getElementById('textArea').value = '';
            // 同时清空服务器存储的文本
            fetch('/update-text', {
                method: 'POST',
                body: new FormData()
            });
        }

        function generateQR() {
            const urlInput = document.getElementById('urlInput');
            const qrResult = document.getElementById('qrResult');
            const url = urlInput.value.trim();

            if (!url) {
                alert('请输入URL！');
                return;
            }

            // 使用QR Server API生成二维码
            const qrImage = document.createElement('img');
            qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(url)}`;
            qrImage.alt = 'QR Code';
            qrImage.className = 'qr-code';

            // 清除之前的二维码（如果有）
            qrResult.innerHTML = '';
            qrResult.appendChild(qrImage);
        }

        function clearURL() {
            document.getElementById('urlInput').value = '';
            document.getElementById('qrResult').innerHTML = '';
        }

        // 页面加载时自动刷新文本
        window.onload = function() {
            refreshText();
        };
    </script>
</body>
</html>
